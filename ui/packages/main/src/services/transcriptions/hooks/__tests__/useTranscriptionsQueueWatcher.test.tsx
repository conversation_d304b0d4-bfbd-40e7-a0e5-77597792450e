import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { renderHook } from '@testing-library/react';

import { logger } from '@carepatron/utilities';

import { isTabAlive, startTabCoordination } from '../../sessionUtils';
import TranscriptionsQueue from '../../transcriptionsQueue';
import { TranscriptionStateManager } from '../../transcriptionStateManager';
import useCompleteTranscriptionParts from '../useCompleteTranscriptionParts';
// Import the hook after mocking
import useTranscriptionsQueueWatcher from '../useTranscriptionsQueueWatcher';

// Mock dependencies
jest.mock('@carepatron/utilities', () => ({
	logger: {
		info: jest.fn(),
		error: jest.fn(),
		warn: jest.fn(),
	},
}));

jest.mock('../../sessionUtils', () => ({
	getTabSessionId: jest.fn(() => 'current-session-123'),
	isTabAlive: jest.fn(),
	startTabCoordination: jest.fn(() => jest.fn()),
}));

jest.mock('../../transcriptionsQueue', () => {
	const mockQueue = {
		getInstance: jest.fn(),
		getAllItems: jest.fn(),
		updateItem: jest.fn(),
		peekAndLockFront: jest.fn(),
		deleteByKey: jest.fn(),
		unlockFront: jest.fn(),
	};
	return {
		__esModule: true,
		default: {
			getInstance: jest.fn(() => mockQueue),
		},
	};
});

jest.mock('../../transcriptionStateManager', () => ({
	TranscriptionStateManager: {
		markTranscriptionActive: jest.fn(),
	},
}));

jest.mock('../useCompleteTranscriptionParts', () => {
	return jest.fn(() => jest.fn());
});

jest.mock('store', () => ({
	useAppSelector: jest.fn(() => 'test-provider-123'),
}));

jest.mock('store/slices/features/hooks', () => ({
	useFeatureFlag: jest.fn(() => ({ hasFeatureFlagged: false })),
}));

jest.mock('services/api', () => ({
	uploadTranscriptionPart: jest.fn(),
}));

describe('useTranscriptionsQueueWatcher - Orphan Adoption', () => {
	const mockQueue = {
		getAllItems: jest.fn(),
		updateItem: jest.fn(),
		peekAndLockFront: jest.fn(),
		deleteByKey: jest.fn(),
		unlockFront: jest.fn(),
	};

	const mockTriggerCompleteParts = jest.fn();
	const currentSessionId = 'current-session-123';

	// Create a minimal store for testing
	const createTestStore = () =>
		configureStore({
			reducer: {
				test: (state = {}) => state,
			},
		});

	const wrapper = ({ children }: { children: React.ReactNode }) => (
		<Provider store={createTestStore()}>{children}</Provider>
	);

	beforeEach(() => {
		jest.clearAllMocks();
		jest.useFakeTimers();

		// Setup mocks
		(TranscriptionsQueue.getInstance as jest.Mock).mockReturnValue(mockQueue);
		(useCompleteTranscriptionParts as jest.Mock).mockReturnValue(mockTriggerCompleteParts);
		(startTabCoordination as jest.Mock).mockReturnValue(jest.fn());

		// Default mock implementations
		mockQueue.getAllItems.mockResolvedValue([]);
		mockQueue.updateItem.mockResolvedValue(undefined);
		mockQueue.peekAndLockFront.mockResolvedValue(null);
		(isTabAlive as jest.Mock).mockResolvedValue(false);
	});

	afterEach(() => {
		jest.useRealTimers();
	});

	// Helper to create mock queue items
	const createMockQueueItem = (sessionId: string, transcriptionId: string, partNumber = 1) => ({
		key: `key-${transcriptionId}-${partNumber}`,
		value: {
			sessionId,
			transcriptionId,
			contactId: 'contact-123',
			noteId: 'note-456',
			audioChunk: new Blob(['test']),
			metadata: {
				partNumber,
				startTime: 0,
				endTime: 1000,
				isLastPart: false,
			},
		},
	});

	describe('orphan adoption mechanism', () => {
		it('should adopt orphaned items from dead tabs', async () => {
			const orphanedSessionId = 'dead-session-456';
			const orphanedItem = createMockQueueItem(orphanedSessionId, 'transcription-789');

			// Mock queue with orphaned item
			mockQueue.getAllItems.mockResolvedValue([orphanedItem]);

			// Mock tab as dead
			(isTabAlive as jest.Mock).mockResolvedValue(false);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			// Fast-forward to trigger adoption check (1 minute interval)
			jest.advanceTimersByTime(60 * 1000);

			// Wait for async operations
			await new Promise((resolve) => setTimeout(resolve, 0));

			expect(isTabAlive).toHaveBeenCalledWith(orphanedSessionId);
			expect(TranscriptionStateManager.markTranscriptionActive).toHaveBeenCalledWith(
				'test-provider-123',
				'transcription-789'
			);
			expect(mockQueue.updateItem).toHaveBeenCalledWith(orphanedItem.key, {
				...orphanedItem.value,
				sessionId: currentSessionId,
			});
		});

		it('should not adopt items from alive tabs', async () => {
			const aliveSessionId = 'alive-session-789';
			const aliveItem = createMockQueueItem(aliveSessionId, 'transcription-456');

			mockQueue.getAllItems.mockResolvedValue([aliveItem]);

			// Mock tab as alive
			(isTabAlive as jest.Mock).mockResolvedValue(true);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			expect(isTabAlive).toHaveBeenCalledWith(aliveSessionId);
			expect(mockQueue.updateItem).not.toHaveBeenCalled();
		});

		it('should not adopt items from current session', async () => {
			const currentSessionItem = createMockQueueItem(currentSessionId, 'transcription-123');

			mockQueue.getAllItems.mockResolvedValue([currentSessionItem]);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			// Should not check if current session is alive
			expect(isTabAlive).not.toHaveBeenCalled();
			expect(mockQueue.updateItem).not.toHaveBeenCalled();
		});

		it('should handle multiple orphaned items', async () => {
			const orphanedItems = [
				createMockQueueItem('dead-session-1', 'transcription-1'),
				createMockQueueItem('dead-session-2', 'transcription-2'),
				createMockQueueItem(currentSessionId, 'transcription-3'), // Should be skipped
			];

			mockQueue.getAllItems.mockResolvedValue(orphanedItems);
			(isTabAlive as jest.Mock).mockResolvedValue(false);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			// Should check alive status for orphaned sessions only
			expect(isTabAlive).toHaveBeenCalledTimes(2);
			expect(isTabAlive).toHaveBeenCalledWith('dead-session-1');
			expect(isTabAlive).toHaveBeenCalledWith('dead-session-2');

			// Should update both orphaned items
			expect(mockQueue.updateItem).toHaveBeenCalledTimes(2);
		});

		it('should handle double-check scenario where tab comes back alive', async () => {
			const sessionId = 'flaky-session-123';
			const orphanedItem = createMockQueueItem(sessionId, 'transcription-456');

			mockQueue.getAllItems.mockResolvedValue([orphanedItem]);

			// First check: tab is dead, second check: tab is alive
			(isTabAlive as jest.Mock)
				.mockResolvedValueOnce(false) // Initial check finds it dead
				.mockResolvedValueOnce(true); // Double-check finds it alive

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			// Should not adopt the item since tab came back alive
			expect(mockQueue.updateItem).not.toHaveBeenCalled();
		});

		it('should handle adoption errors gracefully', async () => {
			const orphanedItem = createMockQueueItem('dead-session-456', 'transcription-789');

			mockQueue.getAllItems.mockResolvedValue([orphanedItem]);
			(isTabAlive as jest.Mock).mockResolvedValue(false);

			// Mock update to fail
			const updateError = new Error('Update failed');
			mockQueue.updateItem.mockRejectedValue(updateError);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			expect(logger.error).toHaveBeenCalledWith(
				'Failed to adopt orphaned item',
				expect.objectContaining({
					transcriptionId: 'transcription-789',
					error: updateError,
				})
			);
		});

		it('should handle getAllItems errors gracefully', async () => {
			const getAllItemsError = new Error('Database error');
			mockQueue.getAllItems.mockRejectedValue(getAllItemsError);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			expect(logger.error).toHaveBeenCalledWith(
				'Error during orphaned items adoption',
				expect.objectContaining({
					error: getAllItemsError,
				})
			);
		});

		it('should use random delays to prevent simultaneous adoption', async () => {
			const orphanedItem = createMockQueueItem('dead-session-456', 'transcription-789');

			mockQueue.getAllItems.mockResolvedValue([orphanedItem]);
			(isTabAlive as jest.Mock).mockResolvedValue(false);

			// Mock Math.random to return a specific value
			const originalRandom = Math.random;
			Math.random = jest.fn(() => 0.5); // 2.5 second delay

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);

			// Should not have adopted yet (still in delay)
			expect(mockQueue.updateItem).not.toHaveBeenCalled();

			// Advance past the delay
			jest.advanceTimersByTime(3000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			expect(mockQueue.updateItem).toHaveBeenCalled();

			// Restore Math.random
			Math.random = originalRandom;
		});
	});

	describe('cleanup and coordination', () => {
		it('should start and cleanup tab coordination', () => {
			const mockCleanup = jest.fn();
			(startTabCoordination as jest.Mock).mockReturnValue(mockCleanup);

			const { unmount } = renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			expect(startTabCoordination).toHaveBeenCalledWith(currentSessionId);

			unmount();

			expect(mockCleanup).toHaveBeenCalled();
		});

		it('should clear intervals on unmount', () => {
			const clearIntervalSpy = jest.spyOn(global, 'clearInterval');

			const { unmount } = renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			unmount();

			expect(clearIntervalSpy).toHaveBeenCalledTimes(2); // Queue processing + adoption intervals
		});
	});
});
