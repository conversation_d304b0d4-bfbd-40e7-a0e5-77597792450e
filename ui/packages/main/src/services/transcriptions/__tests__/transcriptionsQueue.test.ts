import { logger } from '@carepatron/utilities';

import TranscriptionsQueue, { TranscriptionPartQueueMessage } from '../transcriptionsQueue';

// Mock logger
jest.mock('@carepatron/utilities', () => ({
	logger: {
		error: jest.fn(),
		info: jest.fn(),
		warn: jest.fn(),
	},
}));

// Mock IndexedDB
const mockDB = {
	transaction: jest.fn(),
	objectStoreNames: {
		contains: jest.fn(),
	},
	createObjectStore: jest.fn(),
};

const mockObjectStore = {
	put: jest.fn(),
	delete: jest.fn(),
	clear: jest.fn(),
	openCursor: jest.fn(),
};

const mockTransaction = {
	objectStore: jest.fn().mockReturnValue(mockObjectStore),
};

const mockRequest = {
	onsuccess: null as any,
	onerror: null as any,
	result: null as any,
};

const mockOpenRequest = {
	onupgradeneeded: null as any,
	onsuccess: null as any,
	onerror: null as any,
	result: mockDB,
};

// Setup IndexedDB mock
(global as any).indexedDB = {
	open: jest.fn().mockReturnValue(mockOpenRequest),
};

describe('TranscriptionsQueue - updateItem and lock timeout', () => {
	let queue: TranscriptionsQueue;
	const sessionId = 'test-session-123';

	const mockQueueMessage: TranscriptionPartQueueMessage = {
		audioChunk: new Blob(['test audio']),
		noteId: 'note-123',
		contactId: 'contact-456',
		transcriptionId: 'transcription-789',
		sessionId: 'original-session',
		metadata: {
			partNumber: 1,
			startTime: 0,
			endTime: 1000,
			isLastPart: false,
		},
	};

	beforeEach(() => {
		jest.clearAllMocks();
		jest.useFakeTimers();

		// Reset mocks
		mockDB.transaction.mockReturnValue(mockTransaction);
		mockObjectStore.put.mockReturnValue(mockRequest);
		mockObjectStore.delete.mockReturnValue(mockRequest);
		mockObjectStore.clear.mockReturnValue(mockRequest);
		mockObjectStore.openCursor.mockReturnValue(mockRequest);

		// Get queue instance
		queue = TranscriptionsQueue.getInstance(sessionId);

		// Simulate DB initialization
		const openRequest = mockOpenRequest;
		if (openRequest.onsuccess) {
			openRequest.onsuccess({ target: { result: mockDB } } as any);
		}
	});

	afterEach(() => {
		jest.useRealTimers();
		// Clear singleton instances
		(TranscriptionsQueue as any).instances.clear();
	});

	describe('updateItem', () => {
		it('should update an existing item in the queue', async () => {
			const key = 'test-key-123';
			const updatedMessage = {
				...mockQueueMessage,
				sessionId: 'new-session-456',
			};

			// Mock successful put operation
			const putPromise = queue.updateItem(key, updatedMessage);
			mockRequest.onsuccess();

			await putPromise;

			expect(mockDB.transaction).toHaveBeenCalledWith(['queue'], 'readwrite');
			expect(mockObjectStore.put).toHaveBeenCalledWith(updatedMessage, key);
		});

		it('should handle database errors during update', async () => {
			const key = 'test-key-456';
			const error = new Error('Database error');

			const putPromise = queue.updateItem(key, mockQueueMessage);
			mockRequest.onerror({ target: { error } });

			await expect(putPromise).rejects.toThrow('Database error');
		});

		it('should handle null database during update', async () => {
			// Create queue with no DB
			const queueWithoutDB = new (TranscriptionsQueue as any)('test-session');
			(queueWithoutDB as any).db = null;

			const result = await queueWithoutDB.updateItem('key', mockQueueMessage);

			expect(result).toBeNull();
		});
	});

	describe('lock timeout functionality', () => {
		beforeEach(() => {
			// Mock the private properties for testing
			(queue as any).locked = false;
			(queue as any).lockTimestamp = null;
		});

		it('should set lock timestamp when acquiring lock', async () => {
			const currentTime = Date.now();
			jest.spyOn(Date, 'now').mockReturnValue(currentTime);

			// Mock cursor with no results (empty queue)
			const peekPromise = queue.peekAndLockFront();
			mockRequest.onsuccess({ target: { result: null } });

			await peekPromise;

			expect((queue as any).locked).toBe(false); // Should be unlocked after empty result
			expect((queue as any).lockTimestamp).toBeNull();
		});

		it('should respect active lock within timeout period', async () => {
			const currentTime = Date.now();
			jest.spyOn(Date, 'now').mockReturnValue(currentTime);

			// Set lock manually
			(queue as any).locked = true;
			(queue as any).lockTimestamp = currentTime - 10000; // 10 seconds ago (within 30s timeout)

			const result = await queue.peekAndLockFront();

			expect(result).toBeNull();
			expect(mockDB.transaction).not.toHaveBeenCalled();
		});

		it('should allow lock acquisition when timeout has expired', async () => {
			const currentTime = Date.now();
			jest.spyOn(Date, 'now').mockReturnValue(currentTime);

			// Set expired lock
			(queue as any).locked = true;
			(queue as any).lockTimestamp = currentTime - 35000; // 35 seconds ago (beyond 30s timeout)

			// Mock cursor with item
			const mockCursor = {
				primaryKey: 'test-key',
				value: mockQueueMessage,
			};

			const peekPromise = queue.peekAndLockFront();
			mockRequest.onsuccess({ target: { result: mockCursor } });

			const result = await peekPromise;

			expect(result).toEqual({
				key: 'test-key',
				value: mockQueueMessage,
			});
			expect((queue as any).locked).toBe(true);
			expect((queue as any).lockTimestamp).toBe(currentTime);
		});

		it('should clear lock on database error', async () => {
			const error = new Error('Database error');

			// Set lock
			(queue as any).locked = true;
			(queue as any).lockTimestamp = Date.now();

			const peekPromise = queue.peekAndLockFront();
			mockRequest.onerror({ target: { error } });

			await expect(peekPromise).rejects.toThrow('Database error');

			expect((queue as any).locked).toBe(false);
			expect((queue as any).lockTimestamp).toBeNull();
		});

		it('should clear lock when deleteByKey is called', async () => {
			const key = 'test-key-789';

			// Set lock
			(queue as any).locked = true;
			(queue as any).lockTimestamp = Date.now();

			const deletePromise = queue.deleteByKey(key);
			mockRequest.onsuccess();

			await deletePromise;

			expect((queue as any).locked).toBe(false);
			expect((queue as any).lockTimestamp).toBeNull();
		});

		it('should clear lock when clear is called', async () => {
			// Set lock
			(queue as any).locked = true;
			(queue as any).lockTimestamp = Date.now();

			const clearPromise = queue.clear();
			mockRequest.onsuccess();

			await clearPromise;

			expect((queue as any).locked).toBe(false);
			expect((queue as any).lockTimestamp).toBeNull();
		});
	});

	describe('unlockFront', () => {
		it('should clear lock state', () => {
			// Set lock
			(queue as any).locked = true;
			(queue as any).lockTimestamp = Date.now();

			queue.unlockFront();

			expect((queue as any).locked).toBe(false);
			expect((queue as any).lockTimestamp).toBeNull();
		});
	});

	describe('singleton pattern', () => {
		it('should return same instance for same sessionId', () => {
			const instance1 = TranscriptionsQueue.getInstance('same-session');
			const instance2 = TranscriptionsQueue.getInstance('same-session');

			expect(instance1).toBe(instance2);
		});

		it('should return different instances for different sessionIds', () => {
			const instance1 = TranscriptionsQueue.getInstance('session-1');
			const instance2 = TranscriptionsQueue.getInstance('session-2');

			expect(instance1).not.toBe(instance2);
		});
	});
});
